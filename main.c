#ifndef MAIN_H

#define MAIN_H

#endif

#include "main.h"
#include "motor_unified_config.h"  // НОВАЯ УНИФИЦИРОВАННАЯ СИСТЕМА (АКТИВИРОВАНО!)
#include "motor_autocalibration.h" // СИСТЕМА АВТОКАЛИБРОВКИ (АКТИВИРОВАНО!)
#include "motor_predictive_maintenance.h" // СИСТЕМА ПРЕДИКТИВНОГО ОБСЛУЖИВАНИЯ (НОВОЕ!)
// #include "motor_diagnostics.h"  // СИСТЕМА ДИАГНОСТИКИ И МОНИТОРИНГА (пока отключено)

uint8_t u8_Uart1_Cmd = 0;
uint8_t u8_CmdIndex_1 = 0;
uint8_t u8_CmdBuffer_1[64] = {0};  // Увеличен до 64 байт для JSON команд
uint8_t u8_ReceivedCommand[64] = {0};
uint8_t u8_CmdNumber = 0;

uint8_t T_Impulse = 0;
uint8_t T_Pause = 0;

// =================================================================
// НАСТРОЙКИ ВСЕХ МОТОРОВ - МОЖНО МЕНЯТЬ ВРУЧНУЮ
// =================================================================

// M1 - МОТОР ГОРИЗОНТАЛЬНОЙ НАВОДКИ (NEMA23 + редуктор 1:40)
// УСКОРЕННЫЕ НАСТРОЙКИ: ОПТИМИЗИРОВАНО для скорости!
uint16_t M1_StepDelay = 1;       // мс - задержка импульса (оптимальная скорость)
uint16_t M1_PulseWidth = 1;      // мс - ширина импульса (оптимальная скорость)
uint16_t M1_MaxSpeed = 500;      // Гц - УВЕЛИЧЕНА ЧАСТОТА! (было 400, стало 500)

// M2 - МОТОР ВЕРТИКАЛЬНОЙ НАВОДКИ
// УСКОРЕННЫЕ НАСТРОЙКИ: в 2 раза быстрее = 100 Гц (ОПТИМИЗИРОВАНО!)
uint16_t M2_StepDelay_CW = 5;      // мс - УСКОРЕНО В 2 РАЗА! (было 10, стало 5)
uint16_t M2_StepDelay_CCW = 5;     // мс - УСКОРЕНО В 2 РАЗА! (было 10, стало 5)
uint16_t M2_PulseWidth_CCW = 5;    // мс - УСКОРЕНО В 2 РАЗА! (было 10, стало 5)
uint16_t M2_ExtraDelay_CCW = 5;    // мс - УСКОРЕНО В 2 РАЗА! (было 10, стало 5)
uint16_t M2_MaxSpeed = 100;        // Гц - УДВОЕНА СКОРОСТЬ! (было 50, стало 100)

// M3 - МОТОР КАРЕТКИ
// УСКОРЕННЫЕ НАСТРОЙКИ: в 4 раза быстрее = 400 Гц (НЕ ПИЩИТ!)
uint16_t M3_StartStepTime = 500;   // Оригинальные настройки (не используются)
uint16_t M3_StopStepTime = 50;     // Оригинальные настройки (не используются)
uint16_t M3_DeltaStepTime = 20;    // Оригинальные настройки (не используются)
uint16_t M3_StepNumber = 400;      // Оригинальные настройки (не используются)
uint16_t M3_StepDelay = 1;         // мс - МАКСИМАЛЬНАЯ СКОРОСТЬ! (в 10 раз быстрее!)
uint16_t M3_PulseWidth = 1;        // мс - МАКСИМАЛЬНАЯ СКОРОСТЬ! (в 10 раз быстрее!)
uint16_t M3_MaxSpeed = 500;        // Гц - максимальная частота
// M3 БЕЗОПАСНЫЕ НАСТРОЙКИ (микросекунды для стабильной работы без защиты драйвера):
uint16_t M3_StepDelay_uS = 800;    // мкс - БЕЗОПАСНАЯ СКОРОСТЬ! (1250 Гц) - ДРАЙВЕР НЕ УХОДИТ В ЗАЩИТУ!
uint16_t M3_PulseWidth_uS = 800;   // мкс - БЕЗОПАСНАЯ СКОРОСТЬ! (1250 Гц) - ДРАЙВЕР НЕ УХОДИТ В ЗАЩИТУ!

// M4 - МОТОР ПРОДОЛЬНОГО ПЕРЕМЕЩЕНИЯ МЕХАНИЗМА ЗАГРУЗКИ
// МАКСИМАЛЬНЫЕ НАСТРОЙКИ: в 25 раз быстрее = 2500 Гц (НЕ ПИЩИТ!)
uint16_t M4_StartStepTime = 10;   // Ускоренные настройки для разгона
uint16_t M4_StopStepTime = 10;     // Ускоренные настройки для разгона
uint16_t M4_DeltaStepTime = 50;     // Ускоренные настройки для разгона
uint16_t M4_StepNumber = 150;      // Оригинальные настройки (не используются)
uint16_t M4_StepDelay = 1;         // мс - МАКСИМАЛЬНАЯ СКОРОСТЬ! (в 10 раз быстрее!)
uint16_t M4_PulseWidth = 1;        // мс - МАКСИМАЛЬНАЯ СКОРОСТЬ! (в 10 раз быстрее!)
uint16_t M4_MaxSpeed = 500;        // Гц - максимальная частота
// M4 УСКОРЕННЫЕ НАСТРОЙКИ (микросекунды для быстрой работы):
uint16_t M4_StepDelay_uS = 500;    // мкс - УСКОРЕНО! (2000 Гц) - было 800, стало 500
uint16_t M4_PulseWidth_uS = 500;   // мкс - УСКОРЕНО! (2000 Гц) - было 800, стало 500

// M5 - МОТОР ВРАЩЕНИЯ МЕХАНИЗМА ЗАГРУЗКИ
// МАКСИМАЛЬНЫЕ НАСТРОЙКИ: в 25 раз быстрее = 2500 Гц (НЕ ПИЩИТ!)
uint16_t M5_StepDelay = 1;         // мс - МАКСИМАЛЬНАЯ СКОРОСТЬ! (в 10 раз быстрее!)
uint16_t M5_PulseWidth = 1;        // мс - МАКСИМАЛЬНАЯ СКОРОСТЬ! (в 10 раз быстрее!)
uint16_t M5_MaxSpeed = 500;        // Гц - максимальная частота
// M5 ОПТИМАЛЬНЫЕ НАСТРОЙКИ (микросекунды для быстрой но стабильной работы):
uint16_t M5_StepDelay_uS = 600;    // мкс - ОПТИМАЛЬНАЯ СКОРОСТЬ! (1667 Гц) - НЕ ПИЩИТ!
uint16_t M5_PulseWidth_uS = 600;   // мкс - ОПТИМАЛЬНАЯ СКОРОСТЬ! (1667 Гц) - НЕ ПИЩИТ!

// M6 - МОТОР ВРАЩЕНИЯ БАРАБАНА *** МАКСИМАЛЬНАЯ МОЩНОСТЬ ***
// УСКОРЕННЫЕ НАСТРОЙКИ: ОПТИМИЗИРОВАНО для скорости!
uint16_t M6_StepDelay = 1;         // мс - задержка импульса (оптимальная скорость)
uint16_t M6_PulseWidth = 1;        // мс - ширина импульса (оптимальная скорость)
uint16_t M6_MaxSpeed = 1000;       // Гц - УВЕЛИЧЕНА ЧАСТОТА! (было 800, стало 1000)

// M7 - DC МОТОР СЖАТИЯ И РАЗЖАТИЯ МЕХАНИЗМА ЗАХВАТА
// Для DC мотора параметры шагов не используются
uint16_t M7_Timeout = 10000;       // мс - таймаут операции (10 секунд)
uint16_t M7_CheckDelay = 250;      // мс - интервал проверки датчиков

// =================================================================


uint16_t Encoders_Angele = 0;
uint16_t Encoder1_Position = 0;
uint16_t Encoder2_Position = 0;

uint16_t M1_Angele = 0;
uint16_t M2_Angele = 0;

uint16_t Rotate_Angele = 0;

uint8_t M7_Error = 0;
uint8_t SensorPositionError = 0;//D1,D3,D5,D7,D10

uint8_t projectile_number = 0;

int main(void)
{

	uint16_t count1 = 0;
	uint16_t count2 = 0;	

	uint16_t sw = 0;	

	RCCInit();//Set up clocks for peripherals using by App

	SetupTimers();
	
  SetupGpioIO(); //Set up GPIO using by App peripherals	
	
  ADC1_Config();	
	
	SetUp_I2C1();
	
  Delay_mS(5);

/************* Enable INTs in NVIC *****************************/
	 
// For Timer TIM4, TIM3:
   NVIC_EnableIRQ(TIM4_IRQn);  // Enable IRQ from TIM4 for LED PC13 blinking
	 NVIC_SetPriority(TIM4_IRQn,8);
	
// Enable  TIM2 INT in NVIC :	 
   NVIC_EnableIRQ(TIM2_IRQn);  //	
	 NVIC_SetPriority(TIM2_IRQn,2);	

// For UART2	
	 NVIC_SetPriority(USART2_IRQn,10);
	 NVIC_EnableIRQ (USART2_IRQn);
  Delay_mS(5);	 
	
// For UART1	 
	 NVIC_EnableIRQ (USART1_IRQn);
	 NVIC_SetPriority(USART1_IRQn,12);
  Delay_mS(5);	 

  Delay_mS(5);
// For CAN1
   //NVIC_EnableIRQ (CAN1_RX1_IRQn);
	 //NVIC_SetPriority(CAN1_RX1_IRQn,6);

// Set priority for TIM3 Higher then TIM4 and TIM3
	 //NVIC_SetPriority (TIM3_IRQn, 5);

// ��������� ���������� ����������:1
	__enable_irq(); //	
	
/*************************************************************************/	
		//GPIOC -> BSRR |= GPIO_BSRR_BS13; // Set ON LED on PC13

BEEP_ON;
Delay_mS(250);
BEEP_OFF;
Delay_mS(250);

// Инициализация НОВОЙ унифицированной системы конфигурации моторов
Motor_System_Init(); // Инициализация унифицированной системы (АКТИВИРОВАНО!)

// Инициализация системы автокалибровки
AutoCalibration_Init(); // Инициализация системы автокалибровки (АКТИВИРОВАНО!)

// Инициализация системы предиктивного обслуживания
Predictive_Maintenance_Init(); // Инициализация предиктивного обслуживания (НОВОЕ!)

// Инициализация системы диагностики и мониторинга
// Diagnostics_Init(); // Инициализация расширенной диагностики (пока отключено)

Delay_mS(250);
Delay_mS(250);
Delay_mS(250);

    SetUp_I2C1(); //I2C1 enabled here		
    Delay_mS(5);		 
		 

		Signal_3p1D(); 
		Delay_mS(250);
		
		LCD_Setup();
		 
		LCD_SendString((uint8_t *)" CORDON-82 v17.02b ",20);

		Signal_3p1D(); 
		Delay_mS(250);

		LCD_Send_Command(LCD_2_LINE_POS_0);
		LCD_SendString((uint8_t *)" Check system  ",20);
		
		LCD_Send_Command(LCD_3_LINE_POS_0);
		LCD_SendString((uint8_t *)" Wait several secconds  ",20);		

		Signal_3p1D(); 
		Delay_mS(250);
		
		LCD_Send_Command(LCD_4_LINE_POS_0);	
    for(uint8_t i =0; i<20; i++)
 		{
			LCD_SendString((uint8_t *)"'-'",1);
      BEEP_ON;
      Delay_mS(100);
      BEEP_OFF;	
      Delay_mS(250);			
 		}			

Encoder1_Disable;
Encoder2_Disable;	

		LCD_Send_Command(LCD_3_LINE_POS_0);
		LCD_SendString((uint8_t *)"  Wait  Commnds  ",20);
		LCD_Send_Command(LCD_4_LINE_POS_0);	
		
		//UART1 and CMD Tsting:
while(1)
		{
			sw = 0;
			if(u8_Uart1_Cmd)//Handle Commnd from UART:
			  {
					BEEP_ON;
          Delay_mS(250);
          BEEP_OFF;
          Delay_mS(250);
					Delay_mS(250);

					//Save received command:
					SaveReceivedCommand();

					// Проверяем, это JSON команда или бинарная
					if(u8_ReceivedCommand[0] == '{') // JSON команда начинается с '{'
					{
						// Обрабатываем JSON команду
						JSON_ParsedCommand_t json_cmd;
						char json_string[JSON_MAX_BUFFER_SIZE];

						// Копируем команду в строку (предполагаем что команда заканчивается '}')
						uint8_t json_len = 0;
						for(uint8_t i = 0; i < 64 && i < JSON_MAX_BUFFER_SIZE-1; i++) {
							json_string[i] = (char)u8_ReceivedCommand[i];
							json_len++;
							if(u8_ReceivedCommand[i] == '}') break;
						}
						json_string[json_len] = '\0';

						// Парсим и выполняем JSON команду
						if(JSON_Parse(json_string, &json_cmd) == JSON_OK) {
							JSON_ExecuteCommand(&json_cmd);
						} else {
							LCD_Send_Command(LCD_4_LINE_POS_0);
							LCD_SendString((uint8_t *)"JSON Parse Error    ",20);
						}
					}
					else
					{
						// Обрабатываем бинарную команду (старый формат)
						//Define Cmd Number:
						u8_CmdNumber = u8_ReceivedCommand[1];
					
					switch(u8_CmdNumber)
					 {
						case 0:  //«reset» - Return ALL motors to HOME positions
						       Send_To_Main(u8_ReceivedCommand, 7); //Replay
		               LCD_Send_Command(LCD_4_LINE_POS_0);
		               LCD_SendString((uint8_t *)"=== RESET/HOME ===  ",20);

		               // Return all motors to home positions
		               Return_All_Motors_Home();
							break;
						
						case 1:  //�state� Current M1, M2 positions
						       Delay_mS(10);							
						       //Get current angele M1:
						       GetEncoder_1_Angele(); //M1_Angele
						       u8_ReceivedCommand[2] = ((M1_Angele & 0x0300)>> 8);//MSB
						       u8_ReceivedCommand[3] = (M1_Angele & 0x00FF);      //LSB
						       Delay_mS(10);
						       //Get current angele M2
						       GetEncoder_2_Angele(); //M2_Angele
						       u8_ReceivedCommand[4] = ((M2_Angele & 0x0300)>> 8);//MSB
						       u8_ReceivedCommand[5] = (M2_Angele & 0x00FF);      //LSB
						
						        //Send Replay for Cmd_1:	
                   Send_To_Main(u8_ReceivedCommand, 7); //Replay
		               LCD_Send_Command(LCD_4_LINE_POS_0);
		               LCD_SendString((uint8_t *)"   State Received   ",20);						
							break;
						
						case 2:  //rotate_horizontal CW from current position
						           //Send Replay for Cmd_2	
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);
		                LCD_SendString((uint8_t *)"   R_hor_cw Received   ",20);							
							        //Get rotate angele from command:
						        Rotate_Angele = 0;
						        Rotate_Angele |= u8_ReceivedCommand[2];
						        Rotate_Angele <<= 8;
						        Rotate_Angele |= u8_ReceivedCommand[3];
						          //Rotate M1:
						        Rotate_M1_CW(Rotate_Angele);
						
						
							break;
						
						case 3:  //rotate_horizontal CWW from current position 
							              //Send Replay for Cmd_3:	
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);
		                LCD_SendString((uint8_t *)"   R_hor_ccw Received   ",20);							
							     //Get rotate angele from command
						        Rotate_Angele = 0;
						        Rotate_Angele |= u8_ReceivedCommand[2];
						        Rotate_Angele <<= 8;
						        Rotate_Angele |= u8_ReceivedCommand[3];						
						       //Rotate M1
						        Rotate_M1_CCW(Rotate_Angele);
						       //Send Replay for Cmd_3	
                   //Send_To_Main(u8_ReceivedCommand, 7); //Replay						
							break;						
						
						case 4:  //rotate_vertical CW from current position
						           //Send Replay for Cmd_4	
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);
		                LCD_SendString((uint8_t *)"   R_vert_cw Received   ",20);							
							     //Get rotate angele from command	
						        Rotate_Angele = 0;
						        Rotate_Angele |= u8_ReceivedCommand[2];
						        Rotate_Angele <<= 8;
						       //Rotate M2
						        Rotate_M2_CW(Rotate_Angele);
						       //Send Replay for Cmd_4
                    //Send_To_Main(u8_ReceivedCommand, 7); //Replay						
							break;
	
						case 5:  //rotate_vertical CCW from current position
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);	
                    LCD_SendString((uint8_t *)"  R_vert_ccw Received   ",20);						
							         //Get rotate angele from command	
						        Rotate_Angele = 0;
						        Rotate_Angele |= u8_ReceivedCommand[2];
						        Rotate_Angele <<= 8;
						           //Rotate M2
						        Rotate_M2_CCW(Rotate_Angele);						
						           //Send Replay for Cmd_4
                    //Send_To_Main(u8_ReceivedCommand, 7); //Replay						
							break;

						
						case 6:  //�get_projectiles� 
							      projectile_number = 0;
		                LCD_Send_Command(LCD_4_LINE_POS_0);	
                    LCD_SendString((uint8_t *)"   Get_proj Received   ",20);							
							      //u8_ReceivedCommand[2] = 7;
						        for(uint8_t i = 0; i<6; i++)
										 {
						           Rotate_M6_Step(M6_Forward);
										 }
										u8_ReceivedCommand[2] = projectile_number;
							      Send_To_Main(u8_ReceivedCommand, 7); //Replay
							
							break;
						
						case 7:  //«ready»
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);
                    LCD_SendString((uint8_t *)"    READY      ",20);
						        Ready_Command();
						        if(SensorPositionError)
										 {
		                   LCD_Send_Command(LCD_4_LINE_POS_0);
                       LCD_SendString((uint8_t *)"   Sensors Error    ",20);
                       while(1)
 											  {
											  }
										 }

							break;
			//Reserved commands:						
						case 8:  //Rotate_M3 Forward
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);	
                    LCD_SendString((uint8_t *)"Rotate M3 Forward...",20);						
						           //Rotate M3
						        Rotate_M3(M3_Forward);		//Stop if D2					
							break;
						
						case 9:  //Rotate_M3 Back
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);	
                    LCD_SendString((uint8_t *)"Rotate M3 Back......",20);						
						           //Rotate M3
						        Rotate_M3(M3_Back);			//Stop if D1						
							break;
						
						case 10: //Rotate_M4 Forward
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);	
                    LCD_SendString((uint8_t *)"Rotate M4 Forward...",20);						
						           //Rotate M4
						        Rotate_M4(M4_Forward);			//Stop if D8							
							
							break;
						
						case 11:  //Rotate_M4 Back
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);	
                    LCD_SendString((uint8_t *)"Rotate M4 Back......",20);						
						           //Rotate M5
						        Rotate_M4(M4_Back);			//Stop if D9						
							break;
						
						case 12: //Rotate_M5 Forward
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);	
                    LCD_SendString((uint8_t *)"Rotate M5 Forward...",20);						
						           //Rotate M5
						        Rotate_M5(M5_Forward);				//Stop if D6						
							break;
						
						case 13:  //Rotate_M5 Back
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);	
                    LCD_SendString((uint8_t *)"Rotate M5 Back......",20);						
						           //Rotate M5
						        Rotate_M5(M5_Back);			//Stop if D5						
							break;

						case 14: //Rotate_M6 Forward
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);	
                    LCD_SendString((uint8_t *)"Rotate M6 Forward...",20);						
						           //Rotate M6
						        Rotate_M6(M6_Forward);			//Stop if D4							
							break;

						case 15:  //Rotate_M6 Back
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);	
                    LCD_SendString((uint8_t *)"Rotate M6 Back......",20);						
						           //Rotate M6
						        Rotate_M6(M6_Back);					//Stop if D4						
							break;

						case 16: //Rotate_M7 Forward
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);	
                    LCD_SendString((uint8_t *)"Rotate M7 Forward...",20);						
						           //Rotate M7
						        Rotate_M7(M7_Forward);				//Stop if D11	
                    if(M7_Error)
 										{
		                  LCD_Send_Command(LCD_4_LINE_POS_0);	
                      LCD_SendString((uint8_t *)"M7 ERROR............",20);											
 										}
                    else
 										{
		                  LCD_Send_Command(LCD_4_LINE_POS_0);	
                      LCD_SendString((uint8_t *)"M7 STOP.............",20);											
 										}								
							break;

						case 17:  //Rotate_M7 Back
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);
                    LCD_SendString((uint8_t *)"Rotate M7 Back......",20);
						           //Rotate M6
						        Rotate_M7(M7_Back);				//Stop if D10
		                LCD_Send_Command(LCD_4_LINE_POS_0);
                    LCD_SendString((uint8_t *)"M7 STOP.............",20);

							break;

						case 98:  //M6 MAXIMUM POWER TEST
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);
                    LCD_SendString((uint8_t *)"=== M6 MAX POWER ===",20);
                    Delay_mS(250);

                    // Test M6 with maximum power
                    Rotate_M6_Max_Power(M6_Forward, 5000); // 5000 шагов с максимальной мощностью

		                LCD_Send_Command(LCD_4_LINE_POS_0);
                    LCD_SendString((uint8_t *)"=== M6 TEST DONE ===",20);
							break;

						case 96:  //LOAD CONFIG - Load embedded motor configuration
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);
                    LCD_SendString((uint8_t *)"=== LOAD CONFIG ===",20);
                    Delay_mS(250);

                    // Load embedded configuration
                    Load_Embedded_Config();
							break;

						case 97:  //SHOW CONFIG - Show current motor configuration
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);
                    LCD_SendString((uint8_t *)"=== SHOW CONFIG ===",20);
                    Delay_mS(250);

                    // Show current configuration
                    Show_Current_Config();
							break;

							case 85:  //FAST READY - Optimized high-speed READY command
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
			                LCD_Send_Command(LCD_4_LINE_POS_0);
                    LCD_SendString((uint8_t *)"=== FAST READY ===  ",20);
                    Delay_mS(250);

                    // Execute optimized READY command
                    Ready_Command_Fast();
								break;

							case 84:  //SYSTEM DIAGNOSTICS - Show system health (временно отключено)
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
			                LCD_Send_Command(LCD_4_LINE_POS_0);
                    LCD_SendString((uint8_t *)"=== DIAGNOSTICS === ",20);
                    Delay_mS(250);

                    // Show system diagnostics (временно отключено)
                    // Show_System_Diagnostics();
                    // Delay_mS(250); Delay_mS(250); Delay_mS(250); Delay_mS(250); // 1 секунда
                    // Show_Motor_Health_Summary();
                    LCD_Send_Command(LCD_4_LINE_POS_0);
                    LCD_SendString((uint8_t *)"Diag sys disabled   ",20);
								break;

							case 83:  //HEALTH CHECK - Perform full system health check (временно отключено)
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
			                LCD_Send_Command(LCD_4_LINE_POS_0);
                    LCD_SendString((uint8_t *)"=== HEALTH CHECK ===",20);
                    Delay_mS(250);

                    // Perform comprehensive health check (временно отключено)
                    // Perform_System_Health_Check();
                    // Delay_mS(250); Delay_mS(250); // 0.5 секунды
                    // Check_All_Motors_Health();
                    LCD_Send_Command(LCD_4_LINE_POS_0);
                    LCD_SendString((uint8_t *)"Health sys disabled ",20);
								break;

							case 82:  //AUTO CALIBRATE ALL - Автокалибровка всех моторов
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
			                LCD_Send_Command(LCD_4_LINE_POS_0);
                    LCD_SendString((uint8_t *)"=== AUTO CALIB ALL ==",20);
                    Delay_mS(250);

                    // Выполняем автокалибровку всех моторов
                    AutoCalibrate_All_Motors();
								break;

							case 81:  //AUTO CALIBRATE CRITICAL - Быстрая калибровка критичных моторов
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
			                LCD_Send_Command(LCD_4_LINE_POS_0);
                    LCD_SendString((uint8_t *)"=== CALIB CRITICAL ==",20);
                    Delay_mS(250);

                    // Выполняем быструю калибровку критичных моторов (M2, M3, M4)
                    AutoCalibrate_Critical_Motors();
								break;

							case 80:  //AUTO CALIBRATE SINGLE - Калибровка одного мотора (M2)
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
			                LCD_Send_Command(LCD_4_LINE_POS_0);
                    LCD_SendString((uint8_t *)"=== CALIB M2 ONLY ===",20);
                    Delay_mS(250);

                    // Калибруем только M2 (самый проблемный)
                    motor_calibration_result_t result;
                    AutoCalibrate_Motor(2, &result);
								break;

							case 79:  //HEALTH REPORT - Отчет о состоянии критичных моторов
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
			                LCD_Send_Command(LCD_4_LINE_POS_0);
                    LCD_SendString((uint8_t *)"=== HEALTH REPORT ===",20);
                    Delay_mS(250);

                    // Генерируем отчет о здоровье M2 (самый проблемный)
                    Generate_Health_Report(2);
								break;

							case 78:  //MAINTENANCE FORECAST - Прогноз обслуживания
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
			                LCD_Send_Command(LCD_4_LINE_POS_0);
                    LCD_SendString((uint8_t *)"=== FORECAST ===     ",20);
                    Delay_mS(250);

                    // Показываем прогноз обслуживания
                    Show_Maintenance_Forecast();
								break;

							case 77:  //PREDICTIVE ANALYSIS - Предиктивный анализ всех моторов
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
			                LCD_Send_Command(LCD_4_LINE_POS_0);
                    LCD_SendString((uint8_t *)"=== PREDICTIVE ===  ",20);
                    Delay_mS(250);

                    // Выполняем предиктивный анализ для критичных моторов
                    for(uint8_t motor = 2; motor <= 4; motor++) {
                        Collect_Motor_Data(motor);
                        maintenance_prediction_t pred = Predict_Motor_Maintenance(motor);
                        if(pred.maintenance_priority >= 3) {
                            Send_Maintenance_Alert(motor, MAINTENANCE_STATUS_WARNING);
                        }
                    }
								break;

						case 93:  //SHOW ABOUT PAGE - Show contacts, copyright and play melody
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);
                    LCD_SendString((uint8_t *)"=== ABOUT PAGE ===",20);
                    Delay_mS(250);
                    Delay_mS(250);

                    // Show About Page with contacts and looped melody
                    Show_About_Page();

							break;

						case 94:  //PLAY PIANO MELODY - Play piano melody from image
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);
                    LCD_SendString((uint8_t *)"=== PIANO MELODY ===",20);

                    // Play piano melody: 112135-25678975-13(10)(11)3-12(12)8
                    Play_Piano_Melody();

							break;

						case 95:  //PLAY MELODY - Play startup melody
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);
                    LCD_SendString((uint8_t *)"=== PLAYING MELODY ==",20);

                    // Play startup melody
                    for(uint8_t i =0; i<20; i++)
                    {
                        BEEP_ON;
                        Delay_mS(100);
                        BEEP_OFF;
                        Delay_mS(250);
                    }

		                LCD_Send_Command(LCD_4_LINE_POS_0);
                    LCD_SendString((uint8_t *)"=== MELODY DONE ===",20);
							break;

						case 90:  //M1 SIMPLE TEST - только M1 без энкодеров
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);
                    LCD_SendString((uint8_t *)"=== M1 SIMPLE TEST ===",20);
                    Delay_mS(250);

                    // Test M1 simple - без энкодеров и датчиков
                    Test_M1_Simple();

		                LCD_Send_Command(LCD_4_LINE_POS_0);
                    LCD_SendString((uint8_t *)"=== M1 TEST DONE ===",20);
							break;

						case 99:  //TEST - All motors at maximum speed, M6 at maximum power
                    Send_To_Main(u8_ReceivedCommand, 7); //Replay
		                LCD_Send_Command(LCD_4_LINE_POS_0);
                    LCD_SendString((uint8_t *)"=== TEST SCENARIO ===",20);
                    Delay_mS(250);

                    // Test all motors at maximum speed
                    Test_All_Motors_Max_Speed();

		                LCD_Send_Command(LCD_4_LINE_POS_0);
                    LCD_SendString((uint8_t *)"=== TEST COMPLETE ===",20);
							break;
			//Service commands:
						case 100:
							break;
						case 101:
							break;
						case 102:
							break;
						case 103:
							break;
						case 104:
							break;
						case 105:
							break;
						
						default: 
							break;
					 } //End OF switch(u8_CmdNumber)

					 //Get ready for new command:
          ClearCmdBuffer();
					u8_Uart1_Cmd = 0;
					u8_CmdIndex_1 = 0;
					} // End of else (binary command processing)
			   }//End OF if(u8_Uart1_Cmd)
				
				 //Commands for testing:
			 else if(!(SW1) || !(SW2) || !(SW3) || !(SW4)  || !(SW5) || !(SW6) )//Command from SW1-SW6
			  {
					sw |= ((SW6) | (SW5) | (SW4) | (SW3) |  (SW2) | (SW1));	
					
				  LCD_Send_Command(LCD_3_LINE_POS_0);
		      LCD_SendString((uint8_t *)"Receiving commands: ",20);					
					
					BEEP_ON;
          Delay_mS(250);
          BEEP_OFF;					
          
					//Define pressed SW 
					sw = sw >> 8;
					sw |= 0x0C;
					sw = ~sw;
					sw &= 0x00FF; 
					switch(sw)
					 {
						case 48:    //SW5+SW6 Pressed - About Page
							     LCD_Send_Command(LCD_4_LINE_POS_0);
							     LCD_SendString((uint8_t *)"=== ABOUT PAGE ===",20);
							     Delay_mS(250);
							     Delay_mS(250);

							     // Show About Page with contacts and looped melody
							     Show_About_Page();

							     // Wait for buttons to be released
							     while(!(SW5) || !(SW6))
							      {
							        Delay_mS(10);
							      }
							     break;

						case 3:     //SW1+SW2 Pressed - READY Command
							     LCD_Send_Command(LCD_4_LINE_POS_0);
							     LCD_SendString((uint8_t *)"=== READY COMMAND ===",20);
							     Delay_mS(250);

							     // Execute Ready Command
							     Ready_Command();

							     if(SensorPositionError)
							      {
							        LCD_Send_Command(LCD_4_LINE_POS_0);
							        LCD_SendString((uint8_t *)"   Sensors Error    ",20);
							        SensorPositionError = 0; // Reset error for next attempt
							      }
							     else
							      {
							        LCD_Send_Command(LCD_4_LINE_POS_0);
							        LCD_SendString((uint8_t *)"=== READY COMPLETE ==",20);
							      }

							     // Wait for buttons to be released
							     while(!(SW1) || !(SW2))
							      {
							        Delay_mS(10);
							      }
							     break;

						case 1:     //SW1 Pressed
							     Encoder1_Enable;
							     //T_Impulse = 5;
						       //T_Pause = 100;
						       LCD_Send_Command(LCD_4_LINE_POS_0);
						       if( (GPIOB->ODR | (GPIO_ODR_ODR1)))
									   {
		                  LCD_SendString((uint8_t *)"  M1 Rotate CW ...  ",20);
									   }
										else if((GPIOB->ODR & (GPIO_ODR_ODR1)) == 0) 
											LCD_SendString((uint8_t *)"  M1 Rotate CCW ...  ",20);
		               //Choose Motor_1:
						        Choose_M1;
		                //GPIOB->ODR |= GPIO_ODR_ODR3;
		                //GPIOB->ODR &= ~(GPIO_ODR_ODR4);
		                //GPIOB->ODR &= ~(GPIO_ODR_ODR5);
	
						        DD16_Enble;
		                //GPIOC->ODR |= GPIO_ODR_ODR1;	//	DD16 Enable		
                    Delay_mS(5);
						
						        Enable_Motor;
			              //GPIOB->ODR &= ~(GPIO_ODR_ODR2);
			                  //Delay 5 uS:
			              for(uint16_t t = 0; t<1000; t++)
			                     {
			                     }
				            //Change dirction:
                    //GPIOB->ODR |= GPIO_ODR_ODR1;
										GPIOB->ODR ^= GPIO_ODR_ODR1;
													 
										//Rotate_CW;
										//Rotate_CCW;
			                   //Delay 5 uS:
			              for(uint16_t t = 0; t<1000; t++)
			                     {
			                     }						
						
							      while(!(SW1))
										  {
												//M1 Rotate - СТАРЫЕ РАБОЧИЕ НАСТРОЙКИ (НЕ ПИЩИТ!)
 					              GPIOB->ODR |= GPIO_ODR_ODR0;
					              Delay_mS(M1_StepDelay);  // Используем переменную из main.c
					              GPIOB->ODR &= (~GPIO_ODR_ODR0);
					              Delay_mS(M1_PulseWidth); // Используем переменную из main.c
												Encoders_Angele = (uint16_t)GPIOD->IDR;
												Encoders_Angele &= 0x03FF;//Maska
												M1_Angele |= Encoders_Angele;
											/*	if(D1 || D2 || D3 || D4 || D5 || D6 || D7 || D8 || D9 || D10 || D11 || D12 || D13 || D14)
												 {
													 break;
												 }*/
											/*	if((Encoders_Angele >= M1_Angele) && (Encoders_Angele <= M1_Angele))
												 {
													 break;
												 }*/
												
										/*		if(Encoders_Angele <= 0x0002)
												 {
													 break;
												 }
												else if( (Encoders_Angele >= 0x03FD) && (Encoders_Angele <= 0x03FF) )
												 {
													 break;
												 }*/
										
										  }
											  //M1 Stop 
						       LCD_Send_Command(LCD_4_LINE_POS_0);
		               LCD_SendString((uint8_t *)"     M1 Stoped...   ",20);											
										//Disable_Motor;
										//GPIOC->ODR |= GPIO_ODR_ODR2;//Disable Motor
										
                    //DD16_Disble;											
										//GPIOC->ODR &= ~(GPIO_ODR_ODR1);	//	DD16		Disable
											Encoder1_Disable;
											//while(!(SW1)){}
                  break;
						
						case 2:     //SW2 Pressed
		               //Choose Motor_2:
						        Encoder2_Enable;
						        Choose_M2;
                    
						        LCD_Send_Command(LCD_4_LINE_POS_0);
						        LCD_SendString((uint8_t *)"       M2 Start     ",20);
						
						        DD16_Enble;
		                //0GPIOC->ODR |= GPIO_ODR_ODR1;	//	DD16 Enable		
                    Delay_mS(5);
						        Enable_Motor;
			              //GPIOB->ODR &= ~(GPIO_ODR_ODR2);//Enable Motor
			                  //Delay 5 uS:
			              for(uint16_t t = 0; t<1000; t++)
			                     {
			                     }
				            //Change dirction:
                    //GPIOB->ODR |= GPIO_ODR_ODR1;
										GPIOB->ODR ^= GPIO_ODR_ODR1;
													 
										//Rotate_CW;
										//Rotate_CCW;
			                   //Delay 5 uS:
			              for(uint16_t t = 0; t<1000; t++)
			                     {
			                     }							
							       while(!(SW2))
										  {
												//M2 Rotate - РАБОЧИЕ НАСТРОЙКИ (НЕ ПИЩИТ!)
 					              GPIOB->ODR |= GPIO_ODR_ODR0;
					              Delay_mS(M2_StepDelay_CW);  // Используем переменную из main.c
					              GPIOB->ODR &= (~GPIO_ODR_ODR0);
					              Delay_mS(M2_StepDelay_CW);  // ВТОРАЯ ЗАДЕРЖКА для M2!
                        //Delay_mS(5);												
										  }	
											  //M2 Stop 
										Disable_Motor;
										//GPIOC->ODR |= GPIO_ODR_ODR2;//Disable Motor

						        LCD_Send_Command(LCD_4_LINE_POS_0);
						        LCD_SendString((uint8_t *)"       M2 Stop      ",20);
											
                    DD16_Disble;											
										//GPIOC->ODR &= ~(GPIO_ODR_ODR1);	//	DD16		Disable
                    Encoder2_Disable;											
                  break;
						
						case 16:    //SW3 Pressed
		               //Choose Motor_3:
						        Choose_M3;

						        LCD_Send_Command(LCD_4_LINE_POS_0);
						        LCD_SendString((uint8_t *)"       M3 Start     ",20);						
						
						        DD16_Enble;
		                //GPIOC->ODR |= GPIO_ODR_ODR1;	//	DD16 Enable		
                    Delay_mS(5);
						
						        Enable_Motor;
			              //GPIOB->ODR &= ~(GPIO_ODR_ODR2);//Enable Motor
			                  //Delay 5 uS:
			              for(uint16_t t = 0; t<1000; t++)
			                     {
			                     }
				            //Change dirction:
                    //GPIOB->ODR |= GPIO_ODR_ODR1;
										GPIOB->ODR ^= GPIO_ODR_ODR1;
													 
										//Rotate_CW;
										//Rotate_CCW;
			                   //Delay 5 uS:
			              for(uint16_t t = 0; t<1000; t++)
			                     {
			                     }										
							       while(!(SW3))
										  {
												//M3 Rotate - УЛЬТРА БЫСТРЫЕ НАСТРОЙКИ! (микросекунды)
 					              GPIOB->ODR |= GPIO_ODR_ODR0;
					              Delay_uS(M3_StepDelay_uS);  // УЛЬТРА БЫСТРО! (2000 Гц)
					              GPIOB->ODR &= (~GPIO_ODR_ODR0);
					              Delay_uS(M3_PulseWidth_uS); // УЛЬТРА БЫСТРО! (2000 Гц)
										  }		
											  //M3 Stop 
										Disable_Motor;
										//GPIOC->ODR |= GPIO_ODR_ODR2;//Disable Motor
						        LCD_Send_Command(LCD_4_LINE_POS_0);
						        LCD_SendString((uint8_t *)"       M3 Stop      ",20);										
                    DD16_Disble;											
										//GPIOC->ODR &= ~(GPIO_ODR_ODR1);	//	DD16		Disable												
                  break;
						
						case 32:    //SW4 Pressed
		               //Choose Motor_4:
						        Choose_M4;
						        LCD_Send_Command(LCD_4_LINE_POS_0);
						        LCD_SendString((uint8_t *)"       M4 Start     ",20);
						        DD16_Enble;
		                //GPIOC->ODR |= GPIO_ODR_ODR1;	//	DD16 Enable		
                    Delay_mS(5);
						
						        Enable_Motor;
			              //GPIOB->ODR &= ~(GPIO_ODR_ODR2);//Enable Motor
			                  //Delay 5 uS:
			              for(uint16_t t = 0; t<1000; t++)
			                     {
			                     }
				            //Change dirction:
                    //GPIOB->ODR |= GPIO_ODR_ODR1;
										GPIOB->ODR ^= GPIO_ODR_ODR1;
													 
										//Rotate_CW;
										//Rotate_CCW;
			                   //Delay 5 uS:
			              for(uint16_t t = 0; t<1000; t++)
			                     {
			                     }								
							       while(!(SW4))
										  {
												//M4 Rotate - УЛЬТРА БЫСТРЫЕ НАСТРОЙКИ! (микросекунды)
 					              GPIOB->ODR |= GPIO_ODR_ODR0;
					              Delay_uS(M4_StepDelay_uS);  // УЛЬТРА БЫСТРО! (2000 Гц)
					              GPIOB->ODR &= (~GPIO_ODR_ODR0);
					              Delay_uS(M4_PulseWidth_uS); // УЛЬТРА БЫСТРО! (2000 Гц)
                      /*  if(M4_StartStepTime > M4_StopStepTime )
												 {
													 M4_StartStepTime -= M4_DeltaStepTime;
												 }*/													
										  }	
											  //M4 Stop 
										Disable_Motor;
										//GPIOC->ODR |= GPIO_ODR_ODR2;//Disable Motor
						        LCD_Send_Command(LCD_4_LINE_POS_0);
						        LCD_SendString((uint8_t *)"       M4 Stop      ",20);
											
                    DD16_Disble;											
										//GPIOC->ODR &= ~(GPIO_ODR_ODR1);	//	DD16		Disable												
                  break;
						
						case 64:    //SW5 Pressed
		               //Choose Motor_5:
						        Choose_M5;
						        LCD_Send_Command(LCD_4_LINE_POS_0);
						        LCD_SendString((uint8_t *)"       M5 Start     ",20);
						
						        DD16_Enble;
		                //GPIOC->ODR |= GPIO_ODR_ODR1;	//	DD16 Enable		
                    Delay_mS(5);
						
						        Enable_Motor;
			              //GPIOB->ODR &= ~(GPIO_ODR_ODR2);//Enable Motor
			                  //Delay 5 uS:
			              for(uint16_t t = 0; t<1000; t++)
			                     {
			                     }
				            //Change dirction:
                    //GPIOB->ODR |= GPIO_ODR_ODR1;
										GPIOB->ODR ^= GPIO_ODR_ODR1;
													 
										//Rotate_CW;
										//Rotate_CCW;
			                   //Delay 5 uS:
			              for(uint16_t t = 0; t<1000; t++)
			                     {
			                     }								
							       while(!(SW5))
										  {
												//M5 Rotate - УЛЬТРА БЫСТРЫЕ НАСТРОЙКИ! (микросекунды)
 					              GPIOB->ODR |= GPIO_ODR_ODR0;
					              Delay_uS(M5_StepDelay_uS);  // УЛЬТРА БЫСТРО! (3333 Гц)
					              GPIOB->ODR &= (~GPIO_ODR_ODR0);
					              Delay_uS(M5_PulseWidth_uS); // УЛЬТРА БЫСТРО! (3333 Гц)
										  }	
											  //M5 Stop 
										Disable_Motor;
										//GPIOC->ODR |= GPIO_ODR_ODR2;//Disable Moto
										DD16_Disble;
						        LCD_Send_Command(LCD_4_LINE_POS_0);
						        LCD_SendString((uint8_t *)"       M5 Stop      ",20);
											
											
										//GPIOC->ODR &= ~(GPIO_ODR_ODR1);	//	DD16		Disable												
                  break;
						
						case 128:   //SW6 Pressed - READY COMMAND
		               LCD_Send_Command(LCD_4_LINE_POS_0);
		               LCD_SendString((uint8_t *)"=== READY COMMAND ===",20);
		               Delay_mS(250);

		               // Выполняем команду READY
		               Ready_Command();

			               // Проверяем ошибки датчиков
			               if(SensorPositionError)
			                {
			                  LCD_Send_Command(LCD_4_LINE_POS_0);
			                  LCD_SendString((uint8_t *)"   Sensors Error    ",20);
			                  BEEP_ON;
			                  Delay_mS(250);
			                  Delay_mS(250);
			                  Delay_mS(250);
			                  Delay_mS(250);
			                  BEEP_OFF;
			                }
			               else
			                {
			                  LCD_Send_Command(LCD_4_LINE_POS_0);
			                  LCD_SendString((uint8_t *)"=== READY DONE ===  ",20);
			                  // Звуковой сигнал успеха
			                  for(uint8_t i = 0; i < 3; i++)
			                   {
			                     BEEP_ON;
			                     Delay_mS(100);
			                     BEEP_OFF;
			                     Delay_mS(100);
			                   }
			                }

			               // Ждем отпускания кнопки
			               while(!(SW6))
			                {
			                  Delay_mS(10);
			                }
                  break;
						
						default:
                  break;													
					 }
					
			  }
		}//END OF While(1), testing

while(1)//Test M7
{
	//DPT Motor testing:
      M7_Stop;
        Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);	
			M7_GO_Left;	
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);	
      M7_Stop;
        Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);	
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);				
      M7_GO_Right	;
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);
				Delay_mS(250);				

}

}//End OF Main


//Some user function:

void SaveReceivedCommand()
{
	for(uint8_t i = 0;i<64; i++)  // Увеличен до 64 байт
	 {
		 u8_ReceivedCommand[i] = u8_CmdBuffer_1[i];
	 }
}

void Delay_mS(uint8_t Delay)
{
 uint16_t Delay_Ticks = 0;
 Delay_Ticks = Delay * 100;
	
 TIM2->CR1 |= TIM_CR1_CEN;   // Start TIM2 count
 while(1)
	{
		if(TIM2->CNT >= Delay_Ticks)
			{
        TIM2->CR1 &= ~TIM_CR1_CEN;   // Stop TIM2 count	
				TIM2->CNT = 0x0000;
        break;						
			}
	}	
}

void Delay_uS(uint16_t Delay)//time in uS 
{
 //uint16_t Delay_Ticks = 0;
 //Delay_Ticks = Delay * 10; //time in uS
	
 TIM3->CNT = 0x0000;
 TIM3->CR1 |= TIM_CR1_CEN;   // Start TIM3 count
 while(1)
	{
		if(TIM3->CNT >= Delay)
			{
        TIM3->CR1 &= ~TIM_CR1_CEN;   // Stop TIM3 count	
				TIM3->CNT = 0x0000;
        break;						
			}
	}	
}

//Converts I2C Data to ASCII string
void Int_To_Char(uint16_t data, uint8_t *buffer)
{
	 //uint16_t pow10[4] = {1000,100,10,1};
	 uint16_t pow10[2] = {10,1};
	 uint8_t count = 0; // 
   uint8_t i = 0;
	 uint8_t *ptr = buffer;
	 
	 do
	 {
		 while(data >= pow10[i])
		  {
			 count++;
			 data = data - pow10[i];
		  }
		 *ptr = count + '0';
			//Ticks_To_Char[i] = count + '0';
			ptr++;
		  i++;
			count = 0;
   }while(i < 2); 
	 
 } // End of Int_To_Char(int Ticks, char *buffer)

void USART1_IRQHandler(void) // ISR of USART1
{
	//Send_To_Main("\r\n INT UART1 \r\n",15);
  if (USART1->SR & USART_SR_RXNE) // If INT from RX buffer
   {
		 	//Send_To_Main("\r\n RX UART1 \r\n",14);
		 //UART1_Silence = 0; // Reset Silence Flag
	   u8_CmdBuffer_1[u8_CmdIndex_1] = (uint8_t)USART1->DR; //

		 // Проверяем тип команды и условие завершения
		 if((u8_CmdIndex_1 == 6) && (u8_CmdBuffer_1[6] == ';') && u8_CmdBuffer_1[0] == 0x24)
		   {
			  // Бинарная команда: $<cmd><p1><p2><p3><p4>;
			  u8_Uart1_Cmd = 1;
				u8_CmdIndex_1 = 0;
		   }
		 else if((u8_CmdBuffer_1[u8_CmdIndex_1] == '}') && (u8_CmdBuffer_1[0] == '{'))
		   {
			  // JSON команда: {"cmd":"ready"}
			  u8_Uart1_Cmd = 1;
				u8_CmdIndex_1 = 0;
		   }
		 else if(u8_CmdIndex_1 < 63) // Защита от переполнения буфера
		   {
			   u8_CmdIndex_1++;
		   }
		 else
		   {
			   // Переполнение буфера - сброс
			   u8_CmdIndex_1 = 0;
		   }
    }
	 USART1->SR;
	 USART1->DR;
 }

void USART2_IRQHandler(void) // ISR of USART2 
{
	//Send_To_Main("\r\n INT UART2 \r\n",15);
  if (USART2->SR & USART_SR_RXNE) // If INT from RX buffer
   { 
		 	//Send_To_Main("\r\n RX UART1 \r\n",14);
		 //UART1_Silence = 0; // Reset Silence Flag
	   //u8_CmdBuffer_2[u8_CmdIndex_2] = (uint8_t)USART1->DR; //
		 
		 //if((u8_CmdIndex_2 == 7) && (u8_CmdBuffer_2[7] == ';'))
		 //if(u8_CmdIndex_2 == 7)
		 //  {
			//  u8_Uart2_Cmd = 1;// �������� �������
		  // }
		 //else 
			//   u8_CmdIndex_2++;
    }
	 USART2->SR;
 }

 void ClearCmdBuffer(void)
 {
	 for(uint8_t i = 0; i<64; i++)  // Увеличен до 64 байт
	  {
			u8_ReceivedCommand[i] = 0x00;
	  }
 }

 