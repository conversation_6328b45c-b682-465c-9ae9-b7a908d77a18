<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Objects\Servo.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Objects\Servo.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 6230001: Last Updated: Sat Jun 14 16:21:30 2025
<BR><P>
<H3>Maximum Stack Usage =       1188 bytes + Unknown(Functions without stacksize, Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
__rt_entry_main &rArr; main &rArr; Motor_System_Init &rArr; LCD_SendString &rArr; LCD_Send_Data &rArr; Delay_mS
<P>
<H3>
Functions with no stack information
</H3><UL>
 <LI><a href="#[5a]">__user_initial_stackheap</a>
</UL>
</UL>
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[1]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1]">NMI_Handler</a><BR>
 <LI><a href="#[2]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[2]">HardFault_Handler</a><BR>
 <LI><a href="#[3]">MemManage_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[3]">MemManage_Handler</a><BR>
 <LI><a href="#[4]">BusFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[4]">BusFault_Handler</a><BR>
 <LI><a href="#[5]">UsageFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[5]">UsageFault_Handler</a><BR>
 <LI><a href="#[6]">SVC_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[6]">SVC_Handler</a><BR>
 <LI><a href="#[7]">DebugMon_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[7]">DebugMon_Handler</a><BR>
 <LI><a href="#[8]">PendSV_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[8]">PendSV_Handler</a><BR>
 <LI><a href="#[9]">SysTick_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[9]">SysTick_Handler</a><BR>
 <LI><a href="#[1c]">ADC1_2_IRQHandler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1c]">ADC1_2_IRQHandler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[1c]">ADC1_2_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[39]">ADC3_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[4]">BusFault_Handler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[1f]">CAN1_RX1_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[20]">CAN1_SCE_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[15]">DMA1_Channel1_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[16]">DMA1_Channel2_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[17]">DMA1_Channel3_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[18]">DMA1_Channel4_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[19]">DMA1_Channel5_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[1a]">DMA1_Channel6_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[1b]">DMA1_Channel7_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[42]">DMA2_Channel1_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[43]">DMA2_Channel2_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[44]">DMA2_Channel3_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[45]">DMA2_Channel4_5_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[7]">DebugMon_Handler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[10]">EXTI0_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[32]">EXTI15_10_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[11]">EXTI1_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[12]">EXTI2_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[13]">EXTI3_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[14]">EXTI4_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[21]">EXTI9_5_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[e]">FLASH_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[3a]">FSMC_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[2]">HardFault_Handler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[2a]">I2C1_ER_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[29]">I2C1_EV_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[2c]">I2C2_ER_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[2b]">I2C2_EV_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[3]">MemManage_Handler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[1]">NMI_Handler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[b]">PVD_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[8]">PendSV_Handler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[f]">RCC_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[33]">RTCAlarm_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[d]">RTC_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[0]">Reset_Handler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[3b]">SDIO_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[2d]">SPI1_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[2e]">SPI2_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[3d]">SPI3_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[6]">SVC_Handler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[9]">SysTick_Handler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[46]">SystemInit</a> from system_stm32f10x.o(.text.SystemInit) referenced from startup_stm32f10x_hd.o(.text)
 <LI><a href="#[c]">TAMPER_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[22]">TIM1_BRK_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[25]">TIM1_CC_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[24]">TIM1_TRG_COM_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[23]">TIM1_UP_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[26]">TIM2_IRQHandler</a> from timers.o(.text.TIM2_IRQHandler) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[27]">TIM3_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[28]">TIM4_IRQHandler</a> from timers.o(.text.TIM4_IRQHandler) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[3c]">TIM5_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[40]">TIM6_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[41]">TIM7_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[35]">TIM8_BRK_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[38]">TIM8_CC_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[37]">TIM8_TRG_COM_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[36]">TIM8_UP_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[3e]">UART4_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[3f]">UART5_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[2f]">USART1_IRQHandler</a> from main.o(.text.USART1_IRQHandler) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[30]">USART2_IRQHandler</a> from main.o(.text.USART2_IRQHandler) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[31]">USART3_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[34]">USBWakeUp_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[1d]">USB_HP_CAN1_TX_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[1e]">USB_LP_CAN1_RX0_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[5]">UsageFault_Handler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[a]">WWDG_IRQHandler</a> from startup_stm32f10x_hd.o(.text) referenced from startup_stm32f10x_hd.o(RESET)
 <LI><a href="#[47]">__main</a> from __main.o(!!!main) referenced from startup_stm32f10x_hd.o(.text)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[47]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(.text)
</UL>
<P><STRONG><a name="[48]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[4a]"></a>__scatterload_rt2</STRONG> (Thumb, 84 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[a9]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[aa]"></a>__scatterload_loop</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[4b]"></a>__scatterload_copy</STRONG> (Thumb, 26 bytes, Stack size unknown bytes, __scatter_copy.o(!!handler_copy), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>
<BR>[Called By]<UL><LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_copy
</UL>

<P><STRONG><a name="[ab]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, __scatter.o(!!handler_null), UNUSED)

<P><STRONG><a name="[ac]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[4f]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[ad]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[ae]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[af]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[b0]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[b1]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000034))

<P><STRONG><a name="[b2]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[b3]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000002))

<P><STRONG><a name="[b4]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[b5]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[b6]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[b7]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[b8]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[b9]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[ba]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[bb]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[bc]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000006))

<P><STRONG><a name="[bd]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000010))

<P><STRONG><a name="[be]"></a>__rt_lib_init_relocate_pie_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[bf]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000035))

<P><STRONG><a name="[c0]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[c1]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000027))

<P><STRONG><a name="[c2]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[54]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[c3]"></a>__rt_lib_shutdown_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000002))

<P><STRONG><a name="[c4]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000007))

<P><STRONG><a name="[c5]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F))

<P><STRONG><a name="[c6]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000010))

<P><STRONG><a name="[c7]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A))

<P><STRONG><a name="[c8]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000004))

<P><STRONG><a name="[c9]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C))

<P><STRONG><a name="[49]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
<LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
</UL>

<P><STRONG><a name="[ca]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[4c]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[4e]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[cb]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[50]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 1188 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; Motor_System_Init &rArr; LCD_SendString &rArr; LCD_Send_Data &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[cc]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[5b]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[53]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[cd]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[55]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[0]"></a>Reset_Handler</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[1]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[2]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[3]"></a>MemManage_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;MemManage_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[4]"></a>BusFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;BusFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[5]"></a>UsageFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UsageFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[6]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[7]"></a>DebugMon_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DebugMon_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DebugMon_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>SysTick_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>ADC1_2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_2_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[1c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_2_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[39]"></a>ADC3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[1f]"></a>CAN1_RX1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[20]"></a>CAN1_SCE_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>DMA1_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>DMA1_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>DMA1_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>DMA1_Channel4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>DMA1_Channel5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>DMA1_Channel6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>DMA1_Channel7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[42]"></a>DMA2_Channel1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[43]"></a>DMA2_Channel2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[44]"></a>DMA2_Channel3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[45]"></a>DMA2_Channel4_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>EXTI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[32]"></a>EXTI15_10_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>EXTI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>EXTI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>EXTI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>EXTI4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[21]"></a>EXTI9_5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>FLASH_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[3a]"></a>FSMC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[2a]"></a>I2C1_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[29]"></a>I2C1_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[2c]"></a>I2C2_ER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[2b]"></a>I2C2_EV_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>PVD_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>RCC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[33]"></a>RTCAlarm_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>RTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[3b]"></a>SDIO_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[2e]"></a>SPI2_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[3d]"></a>SPI3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>TAMPER_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[22]"></a>TIM1_BRK_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[25]"></a>TIM1_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[24]"></a>TIM1_TRG_COM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[23]"></a>TIM1_UP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[27]"></a>TIM3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[3c]"></a>TIM5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[40]"></a>TIM6_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[41]"></a>TIM7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[35]"></a>TIM8_BRK_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[38]"></a>TIM8_CC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>TIM8_TRG_COM_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[36]"></a>TIM8_UP_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[3e]"></a>UART4_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[3f]"></a>UART5_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[31]"></a>USART3_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[34]"></a>USBWakeUp_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>USB_HP_CAN1_TX_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[1e]"></a>USB_LP_CAN1_RX0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[a]"></a>WWDG_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_stm32f10x_hd.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[5a]"></a>__user_initial_stackheap</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, startup_stm32f10x_hd.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[66]"></a>strcpy</STRONG> (Thumb, 72 bytes, Stack size 12 bytes, strcpy.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = strcpy
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoCalibrate_Motor
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoCalibration_Init
</UL>

<P><STRONG><a name="[83]"></a>__aeabi_memcpy</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memcpy_v6.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Play_Piano_Melody
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_System_Init
</UL>

<P><STRONG><a name="[57]"></a>__rt_memcpy</STRONG> (Thumb, 138 bytes, Stack size 0 bytes, rt_memcpy_v6.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>

<P><STRONG><a name="[ce]"></a>_memcpy_lastbytes</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memcpy_v6.o(.text), UNUSED)

<P><STRONG><a name="[cf]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[d0]"></a>__rt_heap_escrow$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[d1]"></a>__rt_heap_expand$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[58]"></a>__aeabi_memcpy4</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_memcpy
</UL>

<P><STRONG><a name="[d2]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[d3]"></a>__rt_memcpy_w</STRONG> (Thumb, 100 bytes, Stack size 8 bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[d4]"></a>_memcpy_lastbytes_aligned</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rt_memcpy_w.o(.text), UNUSED)

<P><STRONG><a name="[4d]"></a>__user_setup_stackheap</STRONG> (Thumb, 74 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
</UL>
<BR>[Called By]<UL><LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[52]"></a>exit</STRONG> (Thumb, 18 bytes, Stack size 8 bytes, exit.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = exit
</UL>
<BR>[Calls]<UL><LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
</UL>
<BR>[Called By]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[d5]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[59]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[d6]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[56]"></a>_sys_exit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sys_exit.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
</UL>

<P><STRONG><a name="[d7]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[d8]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[d9]"></a>__semihosting_library_function</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, indicate_semi.o(.text), UNUSED)

<P><STRONG><a name="[9f]"></a>ADC1_Config</STRONG> (Thumb, 178 bytes, Stack size 8 bytes, userfunction.o(.text.ADC1_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = ADC1_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[5c]"></a>Apply_Calibration_Results</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, motor_autocalibration.o(.text.Apply_Calibration_Results))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = Apply_Calibration_Results &rArr; LCD_SendString &rArr; LCD_Send_Data &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Set_Motor_Speed
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoCalibrate_Motor
</UL>

<P><STRONG><a name="[61]"></a>AutoCalibrate_All_Motors</STRONG> (Thumb, 372 bytes, Stack size 88 bytes, motor_autocalibration.o(.text.AutoCalibrate_All_Motors))
<BR><BR>[Stack]<UL><LI>Max Depth = 288<LI>Call Chain = AutoCalibrate_All_Motors &rArr; AutoCalibrate_Motor &rArr; Find_Optimal_Speed_Binary &rArr; Test_Motor_Speed_Detailed &rArr; Get_Motor_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Motor_Config
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoCalibrate_Motor
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[63]"></a>AutoCalibrate_Motor</STRONG> (Thumb, 530 bytes, Stack size 80 bytes, motor_autocalibration.o(.text.AutoCalibrate_Motor))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = AutoCalibrate_Motor &rArr; Find_Optimal_Speed_Binary &rArr; Test_Motor_Speed_Detailed &rArr; Get_Motor_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_Calibration_Results
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Restore_Original_Settings
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Apply_Calibration_Results
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Test_Motor_Speed_Detailed
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Find_Optimal_Speed_Binary
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Find_Max_Safe_Speed
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_Calibration_Progress
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Motor_Config
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Check_Motor_Ready_For_Calibration
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_System_MS
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoCalibration_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoCalibrate_All_Motors
</UL>

<P><STRONG><a name="[64]"></a>AutoCalibration_Init</STRONG> (Thumb, 198 bytes, Stack size 24 bytes, motor_autocalibration.o(.text.AutoCalibration_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = AutoCalibration_Init &rArr; LCD_SendString &rArr; LCD_Send_Data &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;strcpy
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoCalibrate_Motor
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[6e]"></a>Auto_Calibrate_All_Motors</STRONG> (Thumb, 180 bytes, Stack size 16 bytes, userfunction.o(.text.Auto_Calibrate_All_Motors))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = Auto_Calibrate_All_Motors &rArr; LCD_SendString &rArr; LCD_Send_Data &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;JSON_ExecuteCommand
</UL>

<P><STRONG><a name="[65]"></a>Check_Motor_Ready_For_Calibration</STRONG> (Thumb, 92 bytes, Stack size 16 bytes, motor_autocalibration.o(.text.Check_Motor_Ready_For_Calibration))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = Check_Motor_Ready_For_Calibration &rArr; Get_Motor_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Motor_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoCalibrate_Motor
</UL>

<P><STRONG><a name="[a8]"></a>ClearCmdBuffer</STRONG> (Thumb, 54 bytes, Stack size 4 bytes, main.o(.text.ClearCmdBuffer))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = ClearCmdBuffer
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[60]"></a>Delay_mS</STRONG> (Thumb, 86 bytes, Stack size 4 bytes, main.o(.text.Delay_mS))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Data
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_4BitCmd
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_Calibration_Results
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Restore_Original_Settings
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Apply_Calibration_Results
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Test_Motor_Speed_Detailed
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Find_Optimal_Speed_Binary
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Find_Max_Safe_Speed
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Auto_Calibrate_All_Motors
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Play_Note
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_All_Motors_Status
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M3_Adaptive
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_Stop_And_Show
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M6_Step_Safe
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RotateM2_D13_Position
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RotateM1_D14_Position
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Test_All_Motors_Max_Speed
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Test_M1_Simple
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Play_Piano_Melody
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_About_Page
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoCalibrate_All_Motors
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_Current_Config
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Load_Embedded_Config
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M6_Max_Power
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M7
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M6
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M5
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M4
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M3
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ready_Command
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M6_Step
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M2_CCW
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M2_CW
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M1_CCW
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M1_CW
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Setup
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Signal_3p1D
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoCalibration_Init
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[85]"></a>Delay_uS</STRONG> (Thumb, 92 bytes, Stack size 4 bytes, main.o(.text.Delay_uS))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = Delay_uS
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Test_Motor_Speed_Detailed
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Play_Note
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M3_Adaptive
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M6_Step_Safe
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Test_All_Motors_Max_Speed
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M5
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M4
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M3
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[69]"></a>Find_Max_Safe_Speed</STRONG> (Thumb, 92 bytes, Stack size 32 bytes, motor_autocalibration.o(.text.Find_Max_Safe_Speed))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = Find_Max_Safe_Speed &rArr; Test_Motor_Speed_Detailed &rArr; Get_Motor_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Test_Motor_Speed_Detailed
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoCalibrate_Motor
</UL>

<P><STRONG><a name="[6a]"></a>Find_Optimal_Speed_Binary</STRONG> (Thumb, 134 bytes, Stack size 48 bytes, motor_autocalibration.o(.text.Find_Optimal_Speed_Binary))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = Find_Optimal_Speed_Binary &rArr; Test_Motor_Speed_Detailed &rArr; Get_Motor_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Test_Motor_Speed_Detailed
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoCalibrate_Motor
</UL>

<P><STRONG><a name="[a6]"></a>GetEncoder_1_Angele</STRONG> (Thumb, 66 bytes, Stack size 0 bytes, userfunction.o(.text.GetEncoder_1_Angele))
<BR><BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a7]"></a>GetEncoder_2_Angele</STRONG> (Thumb, 66 bytes, Stack size 0 bytes, userfunction.o(.text.GetEncoder_2_Angele))
<BR><BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[62]"></a>Get_Motor_Config</STRONG> (Thumb, 48 bytes, Stack size 8 bytes, motor_unified_config.o(.text.Get_Motor_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Get_Motor_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Test_Motor_Speed_Detailed
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Check_Motor_Ready_For_Calibration
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoCalibrate_Motor
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoCalibrate_All_Motors
</UL>

<P><STRONG><a name="[67]"></a>Get_System_MS</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, userfunction.o(.text.Get_System_MS))
<BR><BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoCalibrate_Motor
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_Stop_And_Show
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_Start
</UL>

<P><STRONG><a name="[6f]"></a>JSON_ExecuteCommand</STRONG> (Thumb, 364 bytes, Stack size 24 bytes, json_parser.o(.text.JSON_ExecuteCommand))
<BR><BR>[Stack]<UL><LI>Max Depth = 204<LI>Call Chain = JSON_ExecuteCommand &rArr; Test_All_Motors_Max_Speed &rArr; LCD_SendString &rArr; LCD_Send_Data &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Auto_Calibrate_All_Motors
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M6_Step_Safe
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Test_All_Motors_Max_Speed
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Play_Piano_Melody
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_About_Page
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_Current_Config
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Load_Embedded_Config
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ready_Command
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Return_All_Motors_Home
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[78]"></a>JSON_GetIntValue</STRONG> (Thumb, 182 bytes, Stack size 56 bytes, json_parser.o(.text.JSON_GetIntValue))
<BR><BR>[Stack]<UL><LI>Max Depth = 116<LI>Call Chain = JSON_GetIntValue &rArr; JSON_GetStringValue &rArr; simple_strlen
</UL>
<BR>[Calls]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;JSON_GetStringValue
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;JSON_Parse
</UL>

<P><STRONG><a name="[79]"></a>JSON_GetStringValue</STRONG> (Thumb, 398 bytes, Stack size 48 bytes, json_parser.o(.text.JSON_GetStringValue))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = JSON_GetStringValue &rArr; simple_strlen
</UL>
<BR>[Calls]<UL><LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;simple_strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;JSON_GetIntValue
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;JSON_Parse
</UL>

<P><STRONG><a name="[7b]"></a>JSON_Parse</STRONG> (Thumb, 336 bytes, Stack size 48 bytes, json_parser.o(.text.JSON_Parse))
<BR><BR>[Stack]<UL><LI>Max Depth = 164<LI>Call Chain = JSON_Parse &rArr; JSON_GetIntValue &rArr; JSON_GetStringValue &rArr; simple_strlen
</UL>
<BR>[Calls]<UL><LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;JSON_GetIntValue
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;JSON_GetStringValue
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;JSON_StringToCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[7c]"></a>JSON_StringToCommand</STRONG> (Thumb, 456 bytes, Stack size 16 bytes, json_parser.o(.text.JSON_StringToCommand))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = JSON_StringToCommand &rArr; simple_strcmp
</UL>
<BR>[Calls]<UL><LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;simple_strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;JSON_Parse
</UL>

<P><STRONG><a name="[5f]"></a>LCD_SendString</STRONG> (Thumb, 62 bytes, Stack size 16 bytes, lcd.o(.text.LCD_SendString))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = LCD_SendString &rArr; LCD_Send_Data &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Data
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_Calibration_Results
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Restore_Original_Settings
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Apply_Calibration_Results
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Find_Optimal_Speed_Binary
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Find_Max_Safe_Speed
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_Calibration_Progress
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoCalibrate_Motor
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Auto_Calibrate_All_Motors
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_All_Motors_Status
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;M2_Hold_Position_Active
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M3_Adaptive
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_Stop_And_Show
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_Start
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M6_Step_Safe
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RotateM2_D13_Position
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RotateM1_D14_Position
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Test_All_Motors_Max_Speed
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Test_M1_Simple
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Play_Piano_Melody
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_About_Page
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoCalibrate_All_Motors
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ready_Command_Fast
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_Current_Config
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Load_Embedded_Config
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M7
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M6
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M5
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M4
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M3
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ready_Command
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M1_CCW
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M1_CW
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Return_All_Motors_Home
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;JSON_ExecuteCommand
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoCalibration_Init
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_System_Init
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[7f]"></a>LCD_Send_4BitCmd</STRONG> (Thumb, 262 bytes, Stack size 16 bytes, lcd.o(.text.LCD_Send_4BitCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = LCD_Send_4BitCmd &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Setup
</UL>

<P><STRONG><a name="[5e]"></a>LCD_Send_Command</STRONG> (Thumb, 410 bytes, Stack size 16 bytes, lcd.o(.text.LCD_Send_Command))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = LCD_Send_Command &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_Calibration_Results
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Restore_Original_Settings
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Apply_Calibration_Results
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Find_Optimal_Speed_Binary
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Find_Max_Safe_Speed
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_Calibration_Progress
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoCalibrate_Motor
<LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Auto_Calibrate_All_Motors
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_All_Motors_Status
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;M2_Hold_Position_Active
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M3_Adaptive
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_Stop_And_Show
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_Start
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M6_Step_Safe
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RotateM2_D13_Position
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RotateM1_D14_Position
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Test_All_Motors_Max_Speed
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Test_M1_Simple
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Play_Piano_Melody
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_About_Page
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoCalibrate_All_Motors
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ready_Command_Fast
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_Current_Config
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Load_Embedded_Config
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M7
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M6
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M5
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M4
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M3
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ready_Command
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M1_CCW
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M1_CW
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Return_All_Motors_Home
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;JSON_ExecuteCommand
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Setup
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoCalibration_Init
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_System_Init
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[7e]"></a>LCD_Send_Data</STRONG> (Thumb, 410 bytes, Stack size 16 bytes, lcd.o(.text.LCD_Send_Data))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = LCD_Send_Data &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
</UL>

<P><STRONG><a name="[80]"></a>LCD_Setup</STRONG> (Thumb, 126 bytes, Stack size 16 bytes, lcd.o(.text.LCD_Setup))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = LCD_Setup &rArr; LCD_Send_4BitCmd &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_4BitCmd
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[76]"></a>Load_Embedded_Config</STRONG> (Thumb, 66 bytes, Stack size 24 bytes, userfunction.o(.text.Load_Embedded_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = Load_Embedded_Config &rArr; LCD_SendString &rArr; LCD_Send_Data &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;JSON_ExecuteCommand
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[81]"></a>M2_Hold_Position_Active</STRONG> (Thumb, 36 bytes, Stack size 8 bytes, userfunction.o(.text.M2_Hold_Position_Active))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = M2_Hold_Position_Active &rArr; LCD_SendString &rArr; LCD_Send_Data &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
</UL>
<BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ready_Command
</UL>

<P><STRONG><a name="[82]"></a>Motor_System_Init</STRONG> (Thumb, 1676 bytes, Stack size 592 bytes, motor_unified_config.o(.text.Motor_System_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 628<LI>Call Chain = Motor_System_Init &rArr; LCD_SendString &rArr; LCD_Send_Data &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[84]"></a>Play_Note</STRONG> (Thumb, 278 bytes, Stack size 32 bytes, userfunction.o(.text.Play_Note))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = Play_Note &rArr; Delay_uS
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_uS
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Play_Piano_Melody
</UL>

<P><STRONG><a name="[73]"></a>Play_Piano_Melody</STRONG> (Thumb, 444 bytes, Stack size 56 bytes, userfunction.o(.text.Play_Piano_Melody))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = Play_Piano_Melody &rArr; Play_Note &rArr; Delay_uS
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Play_Note
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_About_Page
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;JSON_ExecuteCommand
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[9c]"></a>RCCInit</STRONG> (Thumb, 198 bytes, Stack size 8 bytes, rcc.o(.text.RCCInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = RCCInit
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[71]"></a>Ready_Command</STRONG> (Thumb, 308 bytes, Stack size 16 bytes, userfunction.o(.text.Ready_Command))
<BR><BR>[Stack]<UL><LI>Max Depth = 108<LI>Call Chain = Ready_Command &rArr; Timer_Stop_And_Show &rArr; LCD_SendString &rArr; LCD_Send_Data &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_All_Motors_Status
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;M2_Hold_Position_Active
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M3_Adaptive
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_Stop_And_Show
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Timer_Start
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M5
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;JSON_ExecuteCommand
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[8b]"></a>Ready_Command_Fast</STRONG> (Thumb, 284 bytes, Stack size 24 bytes, userfunction.o(.text.Ready_Command_Fast))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = Ready_Command_Fast &rArr; RotateM2_D13_Position &rArr; LCD_SendString &rArr; LCD_Send_Data &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RotateM2_D13_Position
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RotateM1_D14_Position
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M5
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M4
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M3
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[6c]"></a>Restore_Original_Settings</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, motor_autocalibration.o(.text.Restore_Original_Settings))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = Restore_Original_Settings &rArr; LCD_SendString &rArr; LCD_Send_Data &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoCalibrate_Motor
</UL>

<P><STRONG><a name="[70]"></a>Return_All_Motors_Home</STRONG> (Thumb, 234 bytes, Stack size 24 bytes, userfunction.o(.text.Return_All_Motors_Home))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = Return_All_Motors_Home &rArr; RotateM2_D13_Position &rArr; LCD_SendString &rArr; LCD_Send_Data &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RotateM2_D13_Position
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RotateM1_D14_Position
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M7
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M6
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M5
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M4
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M3
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
</UL>
<BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;JSON_ExecuteCommand
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[8e]"></a>RotateM1_D14_Position</STRONG> (Thumb, 432 bytes, Stack size 24 bytes, userfunction.o(.text.RotateM1_D14_Position))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = RotateM1_D14_Position &rArr; LCD_SendString &rArr; LCD_Send_Data &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ready_Command_Fast
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Return_All_Motors_Home
</UL>

<P><STRONG><a name="[8f]"></a>RotateM2_D13_Position</STRONG> (Thumb, 416 bytes, Stack size 32 bytes, userfunction.o(.text.RotateM2_D13_Position))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = RotateM2_D13_Position &rArr; LCD_SendString &rArr; LCD_Send_Data &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ready_Command_Fast
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Return_All_Motors_Home
</UL>

<P><STRONG><a name="[92]"></a>Rotate_M1_CCW</STRONG> (Thumb, 510 bytes, Stack size 32 bytes, userfunction.o(.text.Rotate_M1_CCW))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = Rotate_M1_CCW &rArr; LCD_SendString &rArr; LCD_Send_Data &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[93]"></a>Rotate_M1_CW</STRONG> (Thumb, 510 bytes, Stack size 32 bytes, userfunction.o(.text.Rotate_M1_CW))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = Rotate_M1_CW &rArr; LCD_SendString &rArr; LCD_Send_Data &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[94]"></a>Rotate_M2_CCW</STRONG> (Thumb, 374 bytes, Stack size 24 bytes, userfunction.o(.text.Rotate_M2_CCW))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = Rotate_M2_CCW &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[95]"></a>Rotate_M2_CW</STRONG> (Thumb, 366 bytes, Stack size 24 bytes, userfunction.o(.text.Rotate_M2_CW))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = Rotate_M2_CW &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[8c]"></a>Rotate_M3</STRONG> (Thumb, 538 bytes, Stack size 32 bytes, userfunction.o(.text.Rotate_M3))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = Rotate_M3 &rArr; LCD_SendString &rArr; LCD_Send_Data &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_uS
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ready_Command_Fast
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Return_All_Motors_Home
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[88]"></a>Rotate_M3_Adaptive</STRONG> (Thumb, 360 bytes, Stack size 24 bytes, userfunction.o(.text.Rotate_M3_Adaptive))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = Rotate_M3_Adaptive &rArr; LCD_SendString &rArr; LCD_Send_Data &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_uS
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ready_Command
</UL>

<P><STRONG><a name="[8d]"></a>Rotate_M4</STRONG> (Thumb, 536 bytes, Stack size 32 bytes, userfunction.o(.text.Rotate_M4))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = Rotate_M4 &rArr; LCD_SendString &rArr; LCD_Send_Data &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_uS
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ready_Command_Fast
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Return_All_Motors_Home
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[89]"></a>Rotate_M5</STRONG> (Thumb, 540 bytes, Stack size 32 bytes, userfunction.o(.text.Rotate_M5))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = Rotate_M5 &rArr; LCD_SendString &rArr; LCD_Send_Data &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_uS
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ready_Command_Fast
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ready_Command
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Return_All_Motors_Home
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[90]"></a>Rotate_M6</STRONG> (Thumb, 500 bytes, Stack size 32 bytes, userfunction.o(.text.Rotate_M6))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = Rotate_M6 &rArr; LCD_SendString &rArr; LCD_Send_Data &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Return_All_Motors_Home
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[96]"></a>Rotate_M6_Max_Power</STRONG> (Thumb, 312 bytes, Stack size 32 bytes, userfunction.o(.text.Rotate_M6_Max_Power))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = Rotate_M6_Max_Power &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[97]"></a>Rotate_M6_Step</STRONG> (Thumb, 204 bytes, Stack size 24 bytes, userfunction.o(.text.Rotate_M6_Step))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = Rotate_M6_Step &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[74]"></a>Rotate_M6_Step_Safe</STRONG> (Thumb, 506 bytes, Stack size 24 bytes, userfunction.o(.text.Rotate_M6_Step_Safe))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = Rotate_M6_Step_Safe &rArr; LCD_SendString &rArr; LCD_Send_Data &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_uS
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;JSON_ExecuteCommand
</UL>

<P><STRONG><a name="[91]"></a>Rotate_M7</STRONG> (Thumb, 458 bytes, Stack size 16 bytes, userfunction.o(.text.Rotate_M7))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = Rotate_M7 &rArr; LCD_SendString &rArr; LCD_Send_Data &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Return_All_Motors_Home
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a4]"></a>SaveReceivedCommand</STRONG> (Thumb, 62 bytes, Stack size 4 bytes, main.o(.text.SaveReceivedCommand))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = SaveReceivedCommand
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a5]"></a>Send_To_Main</STRONG> (Thumb, 76 bytes, Stack size 12 bytes, userfunction.o(.text.Send_To_Main))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = Send_To_Main
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a0]"></a>SetUp_I2C1</STRONG> (Thumb, 88 bytes, Stack size 0 bytes, i2c.o(.text.SetUp_I2C1))
<BR><BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[5d]"></a>Set_Motor_Speed</STRONG> (Thumb, 96 bytes, Stack size 8 bytes, motor_unified_config.o(.text.Set_Motor_Speed))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Set_Motor_Speed
</UL>
<BR>[Called By]<UL><LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Apply_Calibration_Results
</UL>

<P><STRONG><a name="[9e]"></a>SetupGpioIO</STRONG> (Thumb, 1670 bytes, Stack size 16 bytes, io_gpio.o(.text.SetupGpioIO))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SetupGpioIO
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[9d]"></a>SetupTimers</STRONG> (Thumb, 138 bytes, Stack size 0 bytes, timers.o(.text.SetupTimers))
<BR><BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[72]"></a>Show_About_Page</STRONG> (Thumb, 560 bytes, Stack size 48 bytes, userfunction.o(.text.Show_About_Page))
<BR><BR>[Stack]<UL><LI>Max Depth = 140<LI>Call Chain = Show_About_Page &rArr; Play_Piano_Melody &rArr; Play_Note &rArr; Delay_uS
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Play_Piano_Melody
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;JSON_ExecuteCommand
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[8a]"></a>Show_All_Motors_Status</STRONG> (Thumb, 186 bytes, Stack size 32 bytes, userfunction.o(.text.Show_All_Motors_Status))
<BR><BR>[Stack]<UL><LI>Max Depth = 68<LI>Call Chain = Show_All_Motors_Status &rArr; LCD_SendString &rArr; LCD_Send_Data &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ready_Command
</UL>

<P><STRONG><a name="[68]"></a>Show_Calibration_Progress</STRONG> (Thumb, 120 bytes, Stack size 16 bytes, motor_autocalibration.o(.text.Show_Calibration_Progress))
<BR><BR>[Stack]<UL><LI>Max Depth = 52<LI>Call Chain = Show_Calibration_Progress &rArr; LCD_SendString &rArr; LCD_Send_Data &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoCalibrate_Motor
</UL>

<P><STRONG><a name="[6d]"></a>Show_Calibration_Results</STRONG> (Thumb, 160 bytes, Stack size 24 bytes, motor_autocalibration.o(.text.Show_Calibration_Results))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = Show_Calibration_Results &rArr; LCD_SendString &rArr; LCD_Send_Data &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoCalibrate_Motor
</UL>

<P><STRONG><a name="[77]"></a>Show_Current_Config</STRONG> (Thumb, 226 bytes, Stack size 24 bytes, userfunction.o(.text.Show_Current_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = Show_Current_Config &rArr; LCD_SendString &rArr; LCD_Send_Data &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;JSON_ExecuteCommand
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[9a]"></a>Signal_3p1D</STRONG> (Thumb, 124 bytes, Stack size 32 bytes, userfunction.o(.text.Signal_3p1D))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = Signal_3p1D &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[46]"></a>SystemInit</STRONG> (Thumb, 102 bytes, Stack size 8 bytes, system_stm32f10x.o(.text.SystemInit))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = SystemInit &rArr; SetSysClock &rArr; SetSysClockTo72
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSysClock
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(.text)
</UL>
<P><STRONG><a name="[26]"></a>TIM2_IRQHandler</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, timers.o(.text.TIM2_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[28]"></a>TIM4_IRQHandler</STRONG> (Thumb, 34 bytes, Stack size 0 bytes, timers.o(.text.TIM4_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[75]"></a>Test_All_Motors_Max_Speed</STRONG> (Thumb, 1756 bytes, Stack size 144 bytes, userfunction.o(.text.Test_All_Motors_Max_Speed))
<BR><BR>[Stack]<UL><LI>Max Depth = 180<LI>Call Chain = Test_All_Motors_Max_Speed &rArr; LCD_SendString &rArr; LCD_Send_Data &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_uS
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;JSON_ExecuteCommand
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[9b]"></a>Test_M1_Simple</STRONG> (Thumb, 330 bytes, Stack size 24 bytes, userfunction.o(.text.Test_M1_Simple))
<BR><BR>[Stack]<UL><LI>Max Depth = 60<LI>Call Chain = Test_M1_Simple &rArr; LCD_SendString &rArr; LCD_Send_Data &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[6b]"></a>Test_Motor_Speed_Detailed</STRONG> (Thumb, 774 bytes, Stack size 64 bytes, motor_autocalibration.o(.text.Test_Motor_Speed_Detailed))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = Test_Motor_Speed_Detailed &rArr; Get_Motor_Config
</UL>
<BR>[Calls]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_Motor_Config
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_uS
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Find_Optimal_Speed_Binary
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Find_Max_Safe_Speed
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoCalibrate_Motor
</UL>

<P><STRONG><a name="[86]"></a>Timer_Start</STRONG> (Thumb, 50 bytes, Stack size 8 bytes, userfunction.o(.text.Timer_Start))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = Timer_Start &rArr; LCD_SendString &rArr; LCD_Send_Data &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_System_MS
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
</UL>
<BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ready_Command
</UL>

<P><STRONG><a name="[87]"></a>Timer_Stop_And_Show</STRONG> (Thumb, 422 bytes, Stack size 56 bytes, userfunction.o(.text.Timer_Stop_And_Show))
<BR><BR>[Stack]<UL><LI>Max Depth = 92<LI>Call Chain = Timer_Stop_And_Show &rArr; LCD_SendString &rArr; LCD_Send_Data &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Get_System_MS
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
</UL>
<BR>[Called By]<UL><LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ready_Command
</UL>

<P><STRONG><a name="[2f]"></a>USART1_IRQHandler</STRONG> (Thumb, 256 bytes, Stack size 0 bytes, main.o(.text.USART1_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[30]"></a>USART2_IRQHandler</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, main.o(.text.USART2_IRQHandler))
<BR>[Address Reference Count : 1]<UL><LI> startup_stm32f10x_hd.o(RESET)
</UL>
<P><STRONG><a name="[51]"></a>main</STRONG> (Thumb, 5506 bytes, Stack size 560 bytes, main.o(.text.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 1188<LI>Call Chain = main &rArr; Motor_System_Init &rArr; LCD_SendString &rArr; LCD_Send_Data &rArr; Delay_mS
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_uS
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ClearCmdBuffer
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Test_All_Motors_Max_Speed
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Test_M1_Simple
<LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Play_Piano_Melody
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_About_Page
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoCalibrate_All_Motors
<LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ready_Command_Fast
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Show_Current_Config
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Load_Embedded_Config
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M6_Max_Power
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M7
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M6
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M5
<LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M4
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M3
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Ready_Command
<LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M6_Step
<LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M2_CCW
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M2_CW
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M1_CCW
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Rotate_M1_CW
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetEncoder_2_Angele
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GetEncoder_1_Angele
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Return_All_Motors_Home
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Send_To_Main
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;JSON_ExecuteCommand
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;JSON_Parse
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SaveReceivedCommand
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Send_Command
<LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_SendString
<LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_Setup
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Signal_3p1D
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;AutoCalibration_Init
<LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Motor_System_Init
<LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_mS
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetUp_I2C1
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC1_Config
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetupGpioIO
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetupTimers
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;RCCInit
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__enable_irq
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_SetPriority
<LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[a1]"></a>NVIC_EnableIRQ</STRONG> (Thumb, 10 bytes, Stack size 4 bytes, main.o(.text.NVIC_EnableIRQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a2]"></a>NVIC_SetPriority</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, main.o(.text.NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[a3]"></a>__enable_irq</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, main.o(.text.__enable_irq))
<BR><BR>[Called By]<UL><LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[7d]"></a>simple_strcmp</STRONG> (Thumb, 108 bytes, Stack size 16 bytes, json_parser.o(.text.simple_strcmp))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = simple_strcmp
</UL>
<BR>[Called By]<UL><LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;JSON_StringToCommand
</UL>

<P><STRONG><a name="[7a]"></a>simple_strlen</STRONG> (Thumb, 72 bytes, Stack size 12 bytes, json_parser.o(.text.simple_strlen))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = simple_strlen
</UL>
<BR>[Called By]<UL><LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;JSON_GetStringValue
</UL>

<P><STRONG><a name="[98]"></a>SetSysClock</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, system_stm32f10x.o(.text.SetSysClock))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = SetSysClock &rArr; SetSysClockTo72
</UL>
<BR>[Calls]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSysClockTo72
</UL>
<BR>[Called By]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SystemInit
</UL>

<P><STRONG><a name="[99]"></a>SetSysClockTo72</STRONG> (Thumb, 290 bytes, Stack size 16 bytes, system_stm32f10x.o(.text.SetSysClockTo72))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = SetSysClockTo72
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SetSysClock
</UL>
<P>
<H3>
Undefined Global Symbols
</H3><HR></body></html>
